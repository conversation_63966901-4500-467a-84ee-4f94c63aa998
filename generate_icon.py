#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from PIL import Image, ImageDraw, ImageFilter, ImageEnhance
import os

def process_image_with_style(input_path):
    """处理123.png并生成类似你发的那种样式"""
    try:
        # 打开原始图像
        original = Image.open(input_path)
        print(f"原始图像尺寸: {original.size}")

        # 创建新的画布 (正方形)
        canvas_size = 400
        canvas = Image.new('RGBA', (canvas_size, canvas_size), (0, 0, 0, 0))

        # 创建蓝色渐变背景
        background = create_gradient_background(canvas_size, canvas_size)

        # 将背景粘贴到画布
        canvas.paste(background, (0, 0))

        # 调整原始图像大小并居中放置
        # 保持宽高比
        original_ratio = original.width / original.height
        if original_ratio > 1:
            # 宽图
            new_width = int(canvas_size * 0.6)
            new_height = int(new_width / original_ratio)
        else:
            # 高图或正方形
            new_height = int(canvas_size * 0.6)
            new_width = int(new_height * original_ratio)

        # 调整大小
        resized_original = original.resize((new_width, new_height), Image.Resampling.LANCZOS)

        # 计算居中位置
        x = (canvas_size - new_width) // 2
        y = (canvas_size - new_height) // 2

        # 如果原图有透明通道，直接粘贴；否则转换为RGBA
        if resized_original.mode != 'RGBA':
            resized_original = resized_original.convert('RGBA')

        # 粘贴到画布
        canvas.paste(resized_original, (x, y), resized_original)

        return canvas

    except Exception as e:
        print(f"处理图像时出错: {e}")
        return None

def create_gradient_background(width, height):
    """创建蓝色渐变背景"""
    # 创建一个新的图像
    image = Image.new('RGB', (width, height))
    draw = ImageDraw.Draw(image)

    # 蓝色渐变 (从浅蓝到深蓝)
    color1 = (135, 206, 250)  # 浅蓝色
    color2 = (25, 25, 112)    # 深蓝色

    # 创建垂直渐变
    for y in range(height):
        # 计算渐变比例
        ratio = y / height

        # 插值计算颜色
        r = int(color1[0] * (1 - ratio) + color2[0] * ratio)
        g = int(color1[1] * (1 - ratio) + color2[1] * ratio)
        b = int(color1[2] * (1 - ratio) + color2[2] * ratio)

        # 绘制水平线
        draw.line([(0, y), (width, y)], fill=(r, g, b))

    return image

def draw_house_icon(draw, x, y, size, color=(255, 255, 255)):
    """绘制房屋图标"""
    # 房屋主体 (矩形)
    house_width = size
    house_height = int(size * 0.6)
    house_x = x - house_width // 2
    house_y = y - house_height // 2
    
    # 绘制房屋主体
    draw.rectangle([house_x, house_y, house_x + house_width, house_y + house_height], 
                  fill=color, outline=color)
    
    # 房顶 (三角形)
    roof_height = int(size * 0.4)
    roof_points = [
        (x, house_y - roof_height),  # 顶点
        (house_x - 10, house_y),     # 左下
        (house_x + house_width + 10, house_y)  # 右下
    ]
    draw.polygon(roof_points, fill=color, outline=color)
    
    # 门 (小矩形)
    door_width = int(house_width * 0.25)
    door_height = int(house_height * 0.6)
    door_x = x - door_width // 2
    door_y = house_y + house_height - door_height
    
    # 门的颜色稍微深一点
    door_color = tuple(max(0, c - 50) for c in color)
    draw.rectangle([door_x, door_y, door_x + door_width, door_y + door_height], 
                  fill=door_color, outline=door_color)
    
    # 窗户
    window_size = int(house_width * 0.15)
    window_y = house_y + int(house_height * 0.2)
    
    # 左窗户
    left_window_x = house_x + int(house_width * 0.15)
    draw.rectangle([left_window_x, window_y, 
                   left_window_x + window_size, window_y + window_size], 
                  fill=door_color, outline=door_color)
    
    # 右窗户
    right_window_x = house_x + house_width - int(house_width * 0.15) - window_size
    draw.rectangle([right_window_x, window_y, 
                   right_window_x + window_size, window_y + window_size], 
                  fill=door_color, outline=door_color)

def draw_shield_outline(draw, x, y, size, color=(255, 255, 255), width=3):
    """绘制盾牌轮廓"""
    # 盾牌的点
    shield_width = size
    shield_height = int(size * 1.2)
    
    # 计算盾牌的关键点
    top_y = y - shield_height // 2
    bottom_y = y + shield_height // 2
    left_x = x - shield_width // 2
    right_x = x + shield_width // 2
    
    # 盾牌轮廓点
    points = [
        (x, top_y),  # 顶部中心
        (right_x, top_y + shield_height // 4),  # 右上
        (right_x, bottom_y - shield_height // 4),  # 右下
        (x, bottom_y),  # 底部尖端
        (left_x, bottom_y - shield_height // 4),  # 左下
        (left_x, top_y + shield_height // 4),  # 左上
    ]
    
    # 绘制盾牌轮廓
    draw.polygon(points, outline=color, width=width)

def create_icon_image():
    """创建图标图像"""
    # 图像尺寸
    width, height = 400, 400
    
    # 蓝色渐变 (从浅蓝到深蓝)
    light_blue = (135, 206, 250)  # 浅蓝色
    dark_blue = (25, 25, 112)     # 深蓝色
    
    # 创建渐变背景
    image = create_gradient_background(width, height, light_blue, dark_blue)
    draw = ImageDraw.Draw(image)
    
    # 绘制主要的盾牌和房屋图标 (大)
    main_x, main_y = width // 2, height // 2
    main_size = 120
    
    # 绘制盾牌轮廓
    draw_shield_outline(draw, main_x, main_y, main_size + 20, (255, 255, 255), 4)
    
    # 绘制房屋图标
    draw_house_icon(draw, main_x, main_y, main_size)
    
    # 绘制左上角小图标
    small_x, small_y = 80, 80
    small_size = 40
    draw_shield_outline(draw, small_x, small_y, small_size + 10, (255, 255, 255), 2)
    draw_house_icon(draw, small_x, small_y, small_size)
    
    # 绘制右下角小图标
    small_x2, small_y2 = width - 80, height - 80
    draw_shield_outline(draw, small_x2, small_y2, small_size + 10, (255, 255, 255), 2)
    draw_house_icon(draw, small_x2, small_y2, small_size)
    
    return image

def main():
    """主函数"""
    print("正在生成图标...")
    
    # 创建图标
    icon = create_icon_image()
    
    # 保存图像
    output_path = "generated_icon.png"
    icon.save(output_path, "PNG")
    
    print(f"图标已生成并保存为: {output_path}")
    
    # 也可以显示图像 (如果在支持的环境中)
    try:
        icon.show()
    except:
        print("无法显示图像，但已保存到文件")

if __name__ == "__main__":
    main()
