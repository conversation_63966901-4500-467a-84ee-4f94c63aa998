#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from PIL import Image, ImageDraw
import os

def create_layered_icon_effect(input_path):
    """把123.png生成成多层叠加效果"""
    try:
        # 打开原始图像
        original = Image.open(input_path)
        print(f"原始图像尺寸: {original.size}")

        # 创建画布
        canvas_size = 400
        canvas = Image.new('RGBA', (canvas_size, canvas_size), (0, 0, 0, 0))

        # 创建蓝色背景
        bg_color = (70, 130, 180)  # 钢蓝色
        background = Image.new('RGB', (canvas_size, canvas_size), bg_color)
        canvas.paste(background, (0, 0))

        # 确保原图是RGBA模式
        if original.mode != 'RGBA':
            original = original.convert('RGBA')

        # 创建三个不同大小的图标
        # 大图标 (左上角)
        large_size = 120
        large_icon = original.resize((large_size, large_size), Image.Resampling.LANCZOS)
        large_x, large_y = 50, 50
        canvas.paste(large_icon, (large_x, large_y), large_icon)

        # 中等图标 (中间偏右下)
        medium_size = 100
        medium_icon = original.resize((medium_size, medium_size), Image.Resampling.LANCZOS)
        medium_x, medium_y = 180, 150
        canvas.paste(medium_icon, (medium_x, medium_y), medium_icon)

        # 小图标 (右下角)
        small_size = 80
        small_icon = original.resize((small_size, small_size), Image.Resampling.LANCZOS)
        small_x, small_y = 300, 280
        canvas.paste(small_icon, (small_x, small_y), small_icon)

        return canvas

    except Exception as e:
        print(f"处理图像时出错: {e}")
        return None

def main():
    """主函数"""
    input_file = "123.png"
    output_file = "layered_icon.png"

    if not os.path.exists(input_file):
        print(f"错误: 找不到文件 {input_file}")
        return

    print(f"正在处理 {input_file}...")

    # 生成多层叠加效果
    result = create_layered_icon_effect(input_file)

    if result:
        # 保存结果
        result.save(output_file, "PNG")
        print(f"已生成: {output_file}")
    else:
        print("生成失败")

if __name__ == "__main__":
    main()
